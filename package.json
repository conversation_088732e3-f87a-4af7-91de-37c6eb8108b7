{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3003 --turbo", "build": "next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next": "^15.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}